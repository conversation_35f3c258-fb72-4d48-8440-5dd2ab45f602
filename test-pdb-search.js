// 简单的PDB搜索功能测试脚本
import { createSearchPdbStructures } from './lib/tools/search-pdb-structures.js';

async function testPdbSearch() {
  console.log('开始测试PDB搜索功能...\n');

  try {
    // 创建工具实例
    const [searchPdbStructures] = createSearchPdbStructures();

    // 测试1: 搜索血红蛋白（指定生物体）
    console.log('测试1: 搜索人类血红蛋白');
    const result1 = await searchPdbStructures({
      protein_name: 'hemoglobin',
      organism: 'Homo sapiens'
    });
    const parsed1 = JSON.parse(result1);
    console.log('结果:', parsed1);
    console.log('PDB IDs:', parsed1.pdb_ids?.map(item => item.pdb_id).join(', '));
    console.log('总数量:', parsed1.total_count);
    console.log('');

    // 测试2: 搜索溶菌酶（不指定生物体）
    console.log('测试2: 搜索溶菌酶（所有生物体）');
    const result2 = await searchPdbStructures({
      protein_name: 'lysozyme'
    });
    const parsed2 = JSON.parse(result2);
    console.log('结果:', parsed2);
    console.log('PDB IDs:', parsed2.pdb_ids?.map(item => item.pdb_id).join(', '));
    console.log('总数量:', parsed2.total_count);
    console.log('');

    // 测试3: 搜索不存在的蛋白质
    console.log('测试3: 搜索不存在的蛋白质');
    const result3 = await searchPdbStructures({
      protein_name: 'nonexistentprotein12345'
    });
    const parsed3 = JSON.parse(result3);
    console.log('结果:', parsed3);
    console.log('');

    console.log('所有测试完成！');

  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

// 运行测试
testPdbSearch();
