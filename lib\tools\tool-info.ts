interface ToolInfo {
    description: string;
    params: {
        [key: string]: any;
    };
}



export enum TOOL_NAME {
    GENERATE_MERMAID_CHARTS = 'generate_mermaid_charts',
    GENERATE_ARTIFACTS = 'generate_artifacts',
    CREATE_IMAGES = 'create_images',
    UPLOAD_FILE_TO_SANDBOX = 'upload_file_to_sandbox',
    RUN_PYTHON_CODE = 'run_python_code',
    GENERATE_SMILES_FROM_MOLECULE = 'generate_smiles_from_molecule',

    // search & summary tools
    SEARCH_ONLINE = 'search_online',
    SEARCH_BY_URL = 'search_by_url',
    SEARCH_BY_FILES = 'search_by_files',
    SUMMARIZE_URL = 'summarize_url',
    SUMMARIZE_FILE = 'summarize_file',

    // Twitter / X tools
    GET_X_TWITTER_INFO = 'get_x_twitter_info',
    GET_X_TWITTER_NEWS_SEARCH = 'get_x_twitter_news_search',

    // TOOLS FROM OPENAI_TOOLS.
    CLOCK = 'clock',
    REVERSE_GEOCODE = 'reverseGeocode',
    SHOW_POIS_ON_MAP = 'showPoisOnMap',
}

export const toolNameMapping: Partial<Record<TOOL_NAME, string>> = {
    [TOOL_NAME.GENERATE_MERMAID_CHARTS]: 'MERMAID',
    [TOOL_NAME.GENERATE_SMILES_FROM_MOLECULE]: 'CHEMISTRY',
    [TOOL_NAME.GENERATE_ARTIFACTS]: 'ARTIFACTS',
    [TOOL_NAME.CREATE_IMAGES]: 'IMAGE_CREATION',
    [TOOL_NAME.RUN_PYTHON_CODE]: 'DATA_ANALYSIS',
    [TOOL_NAME.UPLOAD_FILE_TO_SANDBOX]: 'DATA_ANALYSIS',
    [TOOL_NAME.SEARCH_ONLINE]: 'BROWSING',
    [TOOL_NAME.SEARCH_BY_URL]: 'SEARCH_URL',
    [TOOL_NAME.SEARCH_BY_FILES]: 'SEARCH_FILES',
    [TOOL_NAME.SUMMARIZE_URL]: 'SUMMARIZE_URL',
    [TOOL_NAME.SUMMARIZE_FILE]: 'SUMMARIZE_FILE',
    [TOOL_NAME.GET_X_TWITTER_INFO]: 'X_INFO',
    [TOOL_NAME.REVERSE_GEOCODE]: 'MAP_GENERATION',
    [TOOL_NAME.SHOW_POIS_ON_MAP]: 'MAP_GENERATION',
}



export const toolInfoMap = new Map<string, ToolInfo>(
    [
        [
            TOOL_NAME.GET_X_TWITTER_INFO,
            {
                description: `
                ### Get X or Twitter Info based on a X or Twitter url.
                - DO NOT call this tool function if the address for x or twitter is not provided.
                - Input **X_TWITTER_URL**
                    - MUST be a X or Twitter url.for example: https://x.com/Cydiar404
                    - convert @username to https://x.com/username if only username is provided.
                - Returns 
                    - **UserInfo** of give user from url.
                    - **Recent Tweets** List of recent tweets.
                    - **Tweet Content** : If tweet information be found.
                - MUST display the videos that contains in the tweet with <video> tag.
                - MUST display the images that contains in the tweet with <img> tag.
                `,
                params: {
                    X_TWITTER_URL: `a X or Twitter url which is a valid url.`
                }
            }
        ],
        [
            TOOL_NAME.GENERATE_MERMAID_CHARTS,
            {
                description: `
                ### generate mermaid charts
                - ONLY use this tool to generate the following diagrams: Flowchart, sequence diagram, class diagram, state diagram, Gantt chart, ER diagram, mind map, journey map, Git diagram, timeline chart
                - Input **mermaidCode** : A VALID mermaid code.
                `,
                params: {
                    mermaidCode: `
                    ## A VALID mermaid code.
                        - If it is a relationship diagram or a flowchart
                            - MUST to label the edges between the nodes to indicate their relationships.
                            - The positions of the nodes must be arranged reasonably so that the entire graph looks clear and organized, meaning that the edges between nodes should not be too messy.
                        - DO NOT contain any style code of the mermaid,include color,font,size,etc,just use default style.
                        - Strings in mermaidCode CAN NOT contain double quotes.
                        - Prioritize using "\n" as the line break character, which is the safest way.
                        - If HTML tags must be used, ensure they are closed properly using the <br/> form.
                        - In complex charts, try to simplify the text content and reduce the use of special characters.
                        - Test generated charts by first validating with a simple version, then adding complex content.
                        - MUST use english in mermaid code WHEN generate one of the following diagrams: ER Diagram, State Diagram, Gantt
                    `
                }
            }
        ],
        [
            TOOL_NAME.GENERATE_ARTIFACTS,
            {
                description: `
                ### generate artifacts
                - generate artifacts based on the user's prompt.
                - WHEN user want to generate webpage,svg animation,webgl scene,etc, MUST call this tool.
                `,
                params: {
                    prompt: `prompt based on user's request:
                    - DO NOT contain the full html document code in the prompt.
                    - MUST include the artifacts id and the element html will be changed if the user want to modify/tweak the artifact.`,
                    artifactEvent: `the event emitter to emit the artifact.`,
                    historyMessages: `the history messages of the conversation.`
                }
            }
        ],
        [
            TOOL_NAME.UPLOAD_FILE_TO_SANDBOX,
            {
                description: `
                Download and upload the data file you want to analyze to the sandbox based on fileid,
                Returns the Path of the file that uploaded to the sandbox.
                `,
                params: {
                    fileId: `the fildId of the file you want to analyse.`,
                    fileType: `MUST be 'DATA'`,
                },
            },
        ],
        [
            TOOL_NAME.CREATE_IMAGES,
            {
                description: `
                Create Images based on the user's input prompt.
                Determine the need to generate an image based on the user's description and the specification of the image.
                MUST: The returned content is a URL of an image that can be directly rendered through an img tag
                Determine the need to generate an image based on the user's description
                Includes assessments of synonyms and antonyms
                Including the following user queries will trigger entry into the drawing function
                Subject and Object,Environment or Background,Activity or Action,Time and Atmosphere,Colour and Light,Details and Style
                Returns:
                    - imageURL: the url of the image.
                `,
                params: {
                    prompt: `Describe the image what you want.MUST be in english.`,
                },
            },
        ],
        [
            TOOL_NAME.RUN_PYTHON_CODE,
            {
                description: `
                ## Python Sandbox Execution
                1. **Data Analysis with the xlsx、xls、csv file**
                    - **Overview**: Read and output the first 5 valid data rows of the uploaded file from the 'upload_tile_to_sandbox' tool.
                        - **The First 5 valid data rows**: Must use the 'print()' method to do the output.
                    - **Column Names**: List all columns.
                    - **Descriptive Statistics**: Compute mean, sum, standard deviation, min, max, and median.
                    - **Data Counting**: Count total and non-missing entries.
                    - **Data Type and Unique Values**: Identify data types and unique values per field.
                    - **Frequency Analysis**: Analyze frequency of categories.
                2. **handle with the json or xml file**
                    - get all content from json or xml file and then directly return the content to llm.
                3. **Charts Generation**: 
                    - The image that Python generated must not be base64 format.
                    - Characters in the code, including Chinese, Japanese, and Korean, must be set to use the 'WenQuanYi Zen Hei' font globally.You don't need to specify font files.
                    - use \`matplotlib.pyplot\` to generate the charts.(\`import matplotlib.pyplot as plt\`)
                    - DO NOT use \`plt.savefig()\` method to save the charts. but use \`plt.show()\` method to show the charts directly.
                    - **Style**:
                        - NEVER display the grid in the chart.
                        - MUST display the x and y axes.
                    - **Labels, Titles, Legends**:
                        - The labels on the chart should not overlap. The text on the chart should be legible. 
                        - If necessary, separate the legends and annotations of the graphic data to make the charts clear.
                    - **Color schema**:
                        - Never add border to the data elements,like bars,lines, pie etc.
                        - The main colors of the charts must be from the following colors:
                            - ${["#445566", "#445C71", "#446B7C", "#E65D2E", "#E66B4D", "#E67A3D", "#F5E6D3", "#E8F4F2", "#F6D4CD", "#E0F0E9"].join(', ')}
                        - Must use different colors for different data series , different data types or different data categories.
                4. **Python code and execution**: MUST carefully discern the user's intention
                    - If the user merely wants to ask questions related to Python, then simply return the relevant Python code directly. Do not invoke any tool functions to perform any code execution.
                    - If you need to generate data charts or perform mathematical calculations while executing certain tasks, then directly call the tools and do not return the Python code to the user.
                5. **Return**
                    - Outputs the result of code execution.`
                ,
                params: {
                    code: `A VALID Python code snippet. If there are Chinese, Korean or Japanese characters in the code, the font must be globally specified as 'WenQuanYi Zen Hei' in the code, and there is no need to specify the location of the font file.`
                },
            },

        ],
        [
            TOOL_NAME.SEARCH_ONLINE,
            {
                description: `
                Online Search Tool.
                Search the web accroding to the searchQueries. 
                Returns a JSON array of search results.
                `,
                params: {
                    searchQueries: `Three most relevant queries must be generated based on the context.
                    If it is necessary to search for the latest content, the current year and month \`${new Date().getFullYear()}-${new Date().getMonth() + 1}\` must be added to the query.
                    The language of the searchQuery is determined by the user's language.`,
                },
            },
        ],
        [
            TOOL_NAME.SEARCH_BY_URL,
            {
                description: `
                Search the weburls for the relevant context of the searchQuery.
                Make the searchquery as specific and detailed as possible.
                MUST:Display the relevant context directly.
                Returns the relevant context that searched from the weburl.
                MUST:Return the absolute URL path of the image to ensure the image can be displayed directly in the response.
                `,
                params: {
                    searchQuery: `The searchquery should be specific, with more details`,
                    URL: `The web URL you want to search.`,
                },
            },
        ],
        [
            TOOL_NAME.SEARCH_BY_FILES,
            {
                description: `
                Search the files (EXCEPT \`DATA\` files) that associated with the fileIds for the relevant context of the searchQuery.
                MUST:Make the searchquery as specific and detailed as possible
                MUST:Display the relevant context directly
                analysis of file content. Based on user-specified parameters, the system executes tasks including content extraction, keyword identification, and content summarization. 
                The primary purpose of this function is to assist users in quickly understanding and organizing key information contained within the files.
                Returns the relevant context that searched from the files.
                `,
                params: {
                    searchQuery: `The searchquery should be specific, with more details`,
                    files: `An array of files with at least one element, for example:[{fileId:'xxxxxx',fileName:test.pdf,type:'TEXT'},{fileId:'yyyyyy',fileName:test2.pdf,type:'TEXT'},...]`,
                    fileId: `the fildId of the file you want to search.`,
                    fileName: `the name of the file`,
                    fileType: `MUST be 'TEXT' or 'AUDIO'`,
                },
            },
        ],
        [
            TOOL_NAME.SUMMARIZE_URL,
            {
                description: `
                Summarize the weburl.
                MUST: Call this tool only by explicitly saying 'summarize sth.'
                MUST:Return the absolute URL path of the image to ensure the image can be displayed directly in the response.
                Returns the summarized content.
                `,
                params: {
                    URL: `the url address you want to be summarized.`,
                },
            },
        ],
        [
            TOOL_NAME.SUMMARIZE_FILE,
            {
                description: `
                SUMMARIZE the file content.
                MUST: Call this tool only by explicitly saying 'summarize sth.'
                Returns the summarized content.
                `,
                params: {
                    fileId: `the fileId of the file you want to summarize.`,
                    fileType: `MUST be 'TEXT' or 'AUDIO'`,
                },
            },
        ],
        [
            TOOL_NAME.GENERATE_SMILES_FROM_MOLECULE,
            {
                description: `
                ### Generate SMILES from a Chemical Identifier
                - Use this tool to convert a chemical identifier into a canonical SMILES string.
                - This tool is very powerful and can accept a wide range of inputs.
                - ALWAYS prioritize providing the most specific and robust identifier available.
                - For best results, use the molecule's English name, PubChem CID, or CAS number. If you only have the name in another language, please translate it to English first.
                - **Usage Restriction**: This tool generates a SMILES string from a molecule name. The frontend will automatically render the chemical structure based on this string, so you **should not** call the \`CREATE_IMAGES\` tool for visualization. You may proceed with other tasks as needed.
                - **Output Formatting**: When outputting the result, provide ONLY the raw SMILES string itself. DO NOT include any labels like "SMILES string:" or other descriptive text.
                `,
                params: {
                    molecule: `A chemical identifier. The recommended priority for identifiers is:
1.  **CAS Registry Number**: (e.g., "50-78-2" for Aspirin). This is the most reliable method for uncommon or complex molecules.
2.  **IUPAC Name**: (e.g., "1,3,7-trimethylpurine-2,6-dione" for Caffeine).
3.  **Common Name**: (e.g., "water", "caffeine", "aspirin").
4.  **SMILES or InChI**: Can also be used directly.
The name or identifier of the molecule. English names, PubChem CIDs, or CAS numbers are recommended for higher accuracy.`
                }
            }
        ],
    ],

);

