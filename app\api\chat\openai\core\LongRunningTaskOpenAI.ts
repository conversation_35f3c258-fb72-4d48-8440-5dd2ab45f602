import { getCompletion } from "./completion";
import { CONNECTION_SYMBOL, MAX_CONVERSATION_LOOP, MERMAID_SUCCESS_MESSAGE, TOOL_EXECUTION_MESSAGE } from "@/app/api/chat/common/constants";

import callTool from "@/app/api/chat/common/call-tool";

import { OpenAITaskParams } from "@/app/api/chat/common/types";
import { getRelatedSearchQuery } from "@/app/api/chat/common/get-related-search-query";
import { Stream } from "openai/streaming";
import { ChatCompletionChunk, ChatCompletionMessageParam } from "openai/resources/chat/completions";
import { TOOL_NAME, toolNameMapping } from "@/lib/tools/tool-info";
import log from "@/lib/logs";
import { LongRunningTask } from "../../common/LongRunningTask";

export class LongRunningTaskOpenAI extends LongRunningTask<OpenAITaskParams> {
    tmpMessage: string;
    constructor(
        param: OpenAITaskParams,
    ) {
        super(param);
        this.tmpMessage = '';
    }

    async run(): Promise<void> {
        let { body, response, userID, client } = this.param;
        const messages: ChatCompletionMessageParam[] = body.messages;
        let toolMessages: ChatCompletionMessageParam[] = [];

        while (this.shouldContinue && this.count < MAX_CONVERSATION_LOOP) {
            if (this.canceled) {
                return;
            }
            this.tmpMessage = '';
            let r1ReasoningStart = false;
            let r1ReasoningEnd = false;
            this.count++;
            console.log('this.count----->，userID:', userID, this.count);
            const toolCallCollections = [] as {
                [x: string]: any;
                function: {
                    id: string;
                    name: string;
                    arguments: string;
                }
            }[];
            let hasToolCalls = false;
            for await (const part of response) {
                if (userID == '12345') {
                    // console.log('part----->', JSON.stringify(part));
                }
                if (part.choices && part.choices.length > 0) {
                    const tool_calls = part.choices[0]?.delta?.tool_calls;
                    const finish_reason = part.choices[0]?.finish_reason;
                    if (tool_calls || hasToolCalls) {
                        hasToolCalls = true;
                        if (tool_calls) {
                            for (const toolCall of tool_calls) {
                                const index = toolCall.index;
                                const temp = toolCallCollections[index];
                                if (temp) {
                                    temp.function.arguments += toolCall?.function?.arguments || "";
                                    temp.function.name += toolCall?.function?.name || "";
                                } else {
                                    toolCallCollections[index] = {
                                        function: {
                                            id: toolCall.id || "",
                                            name: toolCall?.function?.name || "",
                                            arguments: toolCall?.function?.arguments || "",
                                        }
                                    };
                                }
                            }
                        }

                        if (finish_reason !== "tool_calls") {
                            continue;
                        }

                        for (const toolCallCollection of toolCallCollections) {
                            const toolCall = toolCallCollection.function;

                            const args = toolCall.arguments as unknown as string || "{}";
                            const parsedArgs = JSON.parse(args);

                            let callResult = '';

                            this.emit('progress', `data: ${JSON.stringify({
                                "JUCHATS_ACTION_MSG": {
                                    type: "TOOL_CALL_START",
                                    toolName: toolNameMapping[toolCall.name as TOOL_NAME]
                                }
                            })}\n\n`)

                            const result = await callTool(toolCall.name as TOOL_NAME, parsedArgs, userID);
                            callResult = result.callResult || '';
                            const uuid = result.uuid;
                            log({
                                type: "TOOL_CALL",
                                message: {
                                    type: 'tool_call_result',
                                    uuid,
                                    model: 'OpenAi',
                                    tool: toolCall.name,
                                    callResult,
                                    userID,
                                },
                            })
                            if (toolCall.name == TOOL_NAME.SEARCH_ONLINE) {
                                try {
                                    this.relatedSearchQueries = await getRelatedSearchQuery({ messages });
                                } catch (error) {
                                    this.relatedSearchQueries = [];
                                    log({
                                        type: "ERROR",
                                        message: {
                                            model: 'OpenAI',
                                            message: `getRelatedSearchQuery error:${error}`,
                                            userID,
                                        },
                                    })
                                }



                                try {
                                    // Parse existing and new search results
                                    const parsedResults = this.searchResult
                                        ? [...JSON.parse(this.searchResult), ...JSON.parse(callResult)]
                                        : JSON.parse(callResult);

                                    // Remove contexts and snippets from results if they exist
                                    const cleanedResults = parsedResults.map(({ contexts, ...rest }: { contexts: any, rest: any }) => rest);

                                    // Update search results
                                    this.searchResult = JSON.stringify(cleanedResults);
                                } catch (error) {

                                }

                            }

                            if (toolCall.name == TOOL_NAME.GENERATE_MERMAID_CHARTS && callResult) {
                                const data = JSON.parse(callResult);
                                if (data.message == MERMAID_SUCCESS_MESSAGE) {
                                    this.emit('progress', `data: ${JSON.stringify({
                                        "JUCHATS_ACTION_MSG": {
                                            type: "TOOL_CALL_CONTENT",
                                            toolName: toolNameMapping[toolCall.name as TOOL_NAME],
                                            content: "\n```mermaid\n" + data.mermaidCode + "\n```\n"
                                        }
                                    })}\n\n`)
                                }
                            }

                            if (toolCall.name == TOOL_NAME.GENERATE_SMILES_FROM_MOLECULE && callResult) {
                                try {
                                    const smilesData = JSON.parse(callResult);
                                    if (smilesData.smiles) {
                                        const formattedSmiles = "\n```smiles\n" + smilesData.smiles + "\n```\n";
                                        callResult = formattedSmiles; // Override callResult
                                        this.emit('progress', `data: ${JSON.stringify({
                                            "JUCHATS_ACTION_MSG": {
                                                type: "TOOL_CALL_CONTENT",
                                                toolName: toolNameMapping[toolCall.name as TOOL_NAME],
                                                content: formattedSmiles
                                            }
                                        })}\n\n`)
                                    }
                                } catch {

                                }
                            }

                            if (toolCall.name == TOOL_NAME.SEARCH_PDB_STRUCTURES && callResult) {
                                try {
                                    const pdbData = JSON.parse(callResult);
                                    if (pdbData.pdb_ids && pdbData.pdb_ids.length > 0) {
                                        // 生成多个<juchats-pdb>标签
                                        const pdbTags = pdbData.pdb_ids.map((item: any) =>
                                            `<juchats-pdb>${item.pdb_id}</juchats-pdb>`
                                        ).join('\n');
                                        const formattedPdb = `\n${pdbTags}\n`;
                                        callResult = formattedPdb; // Override callResult
                                        this.emit('progress', `data: ${JSON.stringify({
                                            "JUCHATS_ACTION_MSG": {
                                                type: "TOOL_CALL_CONTENT",
                                                toolName: toolNameMapping[toolCall.name as TOOL_NAME],
                                                content: formattedPdb
                                            }
                                        })}\n\n`)
                                    }
                                } catch {

                                }
                            }

                            this.emit('progress', `data: ${JSON.stringify({
                                "JUCHATS_ACTION_MSG": {
                                    type: "TOOL_CALL_END",
                                    toolName: toolNameMapping[toolCall.name as TOOL_NAME]
                                }
                            })}\n\n`)


                            toolMessages.push(
                                {
                                    role: "assistant",
                                    content: body.model.startsWith('deepseek') ? this.tmpMessage : this.messageInReturn,
                                    tool_calls: [
                                        {
                                            id: toolCall.id,
                                            type: "function",
                                            function: {
                                                name: toolCall.name,
                                                arguments: toolCall.arguments,
                                            },
                                        }
                                    ]
                                },
                                {
                                    role: "tool",
                                    tool_call_id: toolCall.id,
                                    content: toolCall.name == TOOL_NAME.GENERATE_MERMAID_CHARTS ? "generate mermaid chart successfully." : callResult || "",
                                }
                            )

                            messages.push(...toolMessages);

                        }

                        toolCallCollections.length = 0;
                        hasToolCalls = false;
                    } else {


                        if ((part.choices[0].delta as any).reasoning_content != null) {
                            let rContent = (part.choices[0].delta as any).reasoning_content;

                            if (!r1ReasoningStart) {
                                part.choices[0].delta.content = '<juchats-thinking>\n\n' + rContent;
                            } else {
                                part.choices[0].delta.content = rContent;
                            }
                            r1ReasoningStart = true;
                        } else {
                            if (!r1ReasoningEnd) {
                                console.log('this.messageInReturn---->', this.messageInReturn)
                                if (this.messageInReturn.includes('<juchats-thinking>')) {
                                    part.choices[0].delta.content = `</juchats-thinking>\n\n${part.choices[0].delta.content}`;
                                }
                            }

                            r1ReasoningEnd = true;
                        }
                        if (part.choices[0].delta.content) {
                            this.messageInReturn += part.choices[0].delta.content;
                            this.tmpMessage += part.choices[0].delta.content;
                        }

                        this.emit('progress', `data: ${JSON.stringify(part)}\n\n`)

                        if (part.choices[0]?.finish_reason === "stop" || (part.choices[0]?.finish_reason as string) === "eos") {
                            if (this.searchResult) {


                                const searchPart = JSON.stringify({
                                    searchResult: this.searchResult,
                                    relatedSearchQueries: this.relatedSearchQueries,
                                })

                                const newPart = JSON.parse(JSON.stringify(part));
                                newPart.choices[0].delta.content = CONNECTION_SYMBOL + searchPart
                                newPart.choices[0].finish_reason = null;
                                this.searchResult = "";

                                this.messageInReturn += `${CONNECTION_SYMBOL + searchPart}`;
                                this.emit('progress', `data: ${JSON.stringify(newPart)}\n\n`);
                            }
                            log({
                                type: "RESPONSE",
                                message: {
                                    model: "OPENAI",
                                    content: this.messageInReturn,
                                    userID
                                }
                            })
                            this.emit('complete', `data: [DONE]\n\n`);
                            this.shouldContinue = false;

                            break;
                        };
                    }
                }
            }
            if (this.shouldContinue) {
                response = await getCompletion(body, client) as Stream<ChatCompletionChunk>
            }
        }
    }
}

