import { generateOpenAITools } from "@/lib/tools";
import { getStreamResponse } from './core/stream-response';
import { TOOL_NAME } from "@/lib/tools/tool-info";

export async function POST(req: Request) {

    const body = await req.json();
    const authHeader = req.headers.get('Authorization')!;
    const userID = req.headers.get('userID')!;
    const apiKey = authHeader.split('Bearer ')[1];
    console.log('openai req body tools---->', JSON.stringify(body.tools))

    let tools = generateOpenAITools(body.tools || []).filter((tool) => tool.function.name !== TOOL_NAME.GENERATE_ARTIFACTS);
    console.log('openai req body real tools---->', JSON.stringify(tools))
    const params = {
        model: 'gpt-4o-2024-11-20',
        ...body,
        stream: true,
        tools,
        tool_choice: 'auto'
    }

    return await getStreamResponse(
        params,
        userID,
        apiKey
    );
}
