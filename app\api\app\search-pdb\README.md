# PDB 结构搜索功能说明文档

## 1. 功能概述

本功能的核心是提供一个能够从RCSB.org（蛋白质数据银行）搜索蛋白质结构的工具。该工具可以根据蛋白质名称和生物体名称搜索相关的PDB结构，并返回评分最高的前3个PDB ID，供前端加载和渲染3D蛋白质结构。

前端或上游服务可以通过调用相应的API端点，获取指定蛋白质的PDB ID列表，进而用于加载和显示蛋白质的三维结构。

## 2. 实现原理

整个功能实现为一个由AI驱动的工具，其调用链如下：

1.  **AI Agent (上游)**: 当需要搜索蛋白质结构时，AI会根据优化后的工具描述，选择调用 `search_pdb_structures` 工具，并提供蛋白质名称和可选的生物体名称。

2.  **Node.js 中转层 (本项目)**:
    *   接收到工具调用请求，执行 [`lib/tools/search-pdb-structures.ts`](lib/tools/search-pdb-structures.ts) 中的逻辑。
    *   该逻辑会构建RCSB搜索查询并调用RCSB.org的搜索API。

3.  **RCSB.org API调用**:
    *   使用RCSB.org的搜索API (`https://search.rcsb.org/rcsbsearch/v2/query`)。
    *   构建包含全文搜索和生物体过滤的复合查询。
    *   按相关性评分降序排列结果。

4.  **结果返回**:
    *   从搜索结果中提取前3个评分最高的PDB ID。
    *   返回PDB ID列表、评分信息和搜索统计，最终通过 [`/api/app/search-pdb`](./route.ts) 接口返回给调用者。

## 3. 关键文件

-   **工具实现**:
    -   [`lib/tools/search-pdb-structures.ts`](lib/tools/search-pdb-structures.ts): 新增的核心工具，负责构建RCSB查询并处理返回结果。
-   **工具注册与描述**:
    -   [`lib/tools/tool-info.ts`](lib/tools/tool-info.ts): 新增了 `SEARCH_PDB_STRUCTURES` 工具的定义，并为其编写了详尽的描述。
    -   [`lib/tools/index.ts`](lib/tools/index.ts): 将新工具注册到系统的工具映射 (`toolsMap`) 和 `openAITools` 列表中。
-   **API 接口**:
    -   [`app/api/app/search-pdb/route.ts`](./route.ts): 新增的API路由，提供给前端或外部服务调用。

## 4. API 端点说明

-   **URL**: `POST /api/app/search-pdb`
-   **Content-Type**: `application/json`
-   **请求体 (Body)**:
    ```json
    {
      "protein_name": "<protein_name>",
      "organism": "<organism_name>"
    }
    ```
    -   `protein_name`: `string`，必需。要搜索的蛋白质名称（如"hemoglobin", "insulin"）。
    -   `organism`: `string`，可选。生物体的学名（如"Homo sapiens", "Escherichia coli"）。
-   **成功响应 (200 OK)**:
    ```json
    {
      "status": "success",
      "data": {
        "pdb_ids": [
          {
            "pdb_id": "1SHR",
            "score": 0.9947255571053367
          },
          {
            "pdb_id": "1SI4", 
            "score": 0.9947255571053367
          },
          {
            "pdb_id": "1Y01",
            "score": 0.9940754665097618
          }
        ],
        "total_count": 1554,
        "search_query": {
          "protein_name": "hemoglobin",
          "organism": "Homo sapiens"
        }
      }
    }
    ```
    -   `pdb_ids`: 包含PDB ID和相关性评分的数组，最多返回3个结果。
    -   `total_count`: 搜索到的总结果数量。
    -   `search_query`: 实际使用的搜索参数。
-   **失败响应**:
    ```json
    {
      "status": "fail",
      "message": "No PDB structures found for the specified protein"
    }
    ```

## 5. 搜索查询示例

工具内部使用的RCSB查询格式示例：

```json
{
  "query": {
    "type": "group",
    "logical_operator": "and",
    "nodes": [
      {
        "type": "terminal",
        "service": "full_text",
        "parameters": {
          "value": "hemoglobin"
        }
      },
      {
        "type": "terminal",
        "service": "text",
        "parameters": {
          "attribute": "rcsb_entity_source_organism.ncbi_scientific_name",
          "operator": "exact_match",
          "value": "Homo sapiens"
        }
      }
    ]
  },
  "request_options": {
    "paginate": {
      "start": 0,
      "rows": 10
    },
    "sort": [
      {
        "sort_by": "score",
        "direction": "desc"
      }
    ]
  },
  "return_type": "entry"
}
```

## 6. 如何测试

确保本地开发服务器正在运行。

### 使用 PowerShell

```powershell
Invoke-RestMethod -Uri http://localhost:3000/api/app/search-pdb -Method Post -ContentType 'application/json' -Body '{"protein_name": "hemoglobin", "organism": "Homo sapiens"}'
```

### 使用 curl

```bash
curl -X POST http://localhost:3000/api/app/search-pdb \
-H "Content-Type: application/json" \
-d '{"protein_name": "hemoglobin", "organism": "Homo sapiens"}'
```

### 不指定生物体的搜索

```bash
curl -X POST http://localhost:3000/api/app/search-pdb \
-H "Content-Type: application/json" \
-d '{"protein_name": "lysozyme"}'
```
