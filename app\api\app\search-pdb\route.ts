import { fail, success } from '@/lib/core/response'
import { decodeFromUnicode } from '@/lib/utils';
import { createSearchPdbStructures } from '@/lib/tools/search-pdb-structures';
import { FunctionDefinition } from 'openai/resources';

export async function POST(req: Request) {
  const {
    protein_name,
    organism
  }: { protein_name: string; organism?: string; } =
    await req.json()

  try {
    const decodedProteinName = decodeFromUnicode(protein_name);
    const decodedOrganism = organism ? decodeFromUnicode(organism) : undefined;
    
    const runPdbSearch = (createSearchPdbStructures() as [Function, FunctionDefinition])[0];
    const rawResult = await runPdbSearch({ 
      protein_name: decodedProteinName,
      organism: decodedOrganism 
    });
    const parsedResult = JSON.parse(rawResult);

    if (parsedResult.pdb_ids && parsedResult.pdb_ids.length > 0) {
      return success({
        pdb_ids: parsedResult.pdb_ids,
        total_count: parsedResult.total_count,
        search_query: parsedResult.search_query
      });
    } else {
      return fail('No PDB structures found for the specified protein');
    }
  } catch (error) {
    console.error('PDB search API error:', error);
    return fail('Failed to search PDB structures');
  }
}
