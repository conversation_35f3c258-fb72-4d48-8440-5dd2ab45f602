
import { intersectionBy } from 'lodash';
import { create<PERSON>lock, createReverseGeocode, createShowPoisOnMap } from 'openai-function-calling-tools';
import { createSearchOnline } from '@/lib/tools/search-online';
import { createSearchByUrl } from '@/lib/tools/search-by-url';
import { createSearchByFiles } from '@/lib/tools/search-by-files';
import { createSummarizeUrl } from '@/lib/tools/summarize-url';
import { createSummarizeFile } from '@/lib/tools/summarize-file';
import { createRunPythonCode } from '@/lib/tools/run-python-code';
import { createImagesSchemaByprompt } from '@/lib/tools/create-images';
import { createUploadFileToSandbox } from '@/lib/tools/upload-file-to-sandbox';
import { createGenerateArtifacts } from '@/lib/tools/generate-artifacts';
import { createGenerateMermaidCharts } from '@/lib/tools/generate-mermaid-charts';
import { createGenerateSmilesFromMolecule } from '@/lib/tools/generate-smiles-from-molecule';
import { createGetXTwitterInfo } from '@/lib/tools/get-x-twitter-info';
import { Tool as AnthropicTool } from '@anthropic-ai/sdk/resources/index';
import { TOOL_NAME, toolInfoMap } from './tool-info';
import { ChatCompletionTool, FunctionDefinition } from 'openai/resources/index';
import { ToolControlParam, toolsControl } from './tools-control';

const [generateMermaidCharts, generateMermaidChartsSchema] = createGenerateMermaidCharts() as [Function, FunctionDefinition];
const [getXTwitterInfo, getXTwitterInfoSchema] = createGetXTwitterInfo() as [Function, FunctionDefinition];
const [searchOnline, searchOnlineSchema] = createSearchOnline() as [Function, FunctionDefinition];
const [searchByUrl, searchByUrlSchema] = createSearchByUrl() as [Function, FunctionDefinition];
const [searchByFiles, searchByFilesSchema] = createSearchByFiles() as [Function, FunctionDefinition];
const [summarizeUrl, summarizeUrlSchema] = createSummarizeUrl() as [Function, FunctionDefinition];
const [summarizeFile, summarizeFileSchema] = createSummarizeFile() as [Function, FunctionDefinition];
const [reverseGeocode, reverseGeocodeSchema] = createReverseGeocode({
    mapboxAccessToken: process.env.MAPBOX_ACCESS_TOKEN as string,
}) as [Function, FunctionDefinition];
const [showPoisOnMap, showPoisOnMapSchema] = createShowPoisOnMap({
    mapboxAccessToken: process.env.MAPBOX_ACCESS_TOKEN as string,
}) as [Function, FunctionDefinition];
const [clock, clockSchema] = createClock() as [Function, FunctionDefinition];
const [runPythonCode, runPythonCodeSchema] = createRunPythonCode() as [Function, FunctionDefinition];
const [createImages, createImagesSchema] = createImagesSchemaByprompt() as [Function, FunctionDefinition];
const [uploadFileToSandbox, uploadFileToSandboxSchema] = createUploadFileToSandbox() as [Function, FunctionDefinition];
const [generateArtifacts, generateArtifactsSchema] = createGenerateArtifacts() as [Function, FunctionDefinition];
const [generateSmilesFromMolecule, generateSmilesFromMoleculeSchema] = createGenerateSmilesFromMolecule() as [Function, FunctionDefinition];

export const toolsMap = new Map([
    // TOOLS ABOUT search & summary
    [TOOL_NAME.SEARCH_ONLINE, searchOnline],
    [TOOL_NAME.SEARCH_BY_URL, searchByUrl],
    [TOOL_NAME.SEARCH_BY_FILES, searchByFiles],
    [TOOL_NAME.SUMMARIZE_URL, summarizeUrl],
    [TOOL_NAME.SUMMARIZE_FILE, summarizeFile],

    // TOOLS FROM OPENAI_TOOLS.
    [TOOL_NAME.CLOCK, clock],
    [TOOL_NAME.REVERSE_GEOCODE, reverseGeocode],
    [TOOL_NAME.SHOW_POIS_ON_MAP, showPoisOnMap],

    // TOOLS FOR IMAGES
    [TOOL_NAME.CREATE_IMAGES, createImages],
    [TOOL_NAME.GENERATE_MERMAID_CHARTS, generateMermaidCharts],
    [TOOL_NAME.GENERATE_SMILES_FROM_MOLECULE, generateSmilesFromMolecule],


    [TOOL_NAME.GET_X_TWITTER_INFO, getXTwitterInfo],


    // TOOLS ABOUT PYTHON & SANDBOX.
    [TOOL_NAME.RUN_PYTHON_CODE, runPythonCode],
    [TOOL_NAME.UPLOAD_FILE_TO_SANDBOX, uploadFileToSandbox],
    [TOOL_NAME.GENERATE_ARTIFACTS, generateArtifacts],
]);

export const openAITools: ChatCompletionTool[] = [
    {
        type: 'function',
        function: getXTwitterInfoSchema
    },
    {
        type: 'function',
        function: generateMermaidChartsSchema
    },
    {
        type: 'function',
        function: uploadFileToSandboxSchema
    },
    {
        type: 'function',
        function: searchByFilesSchema
    },
    {
        type: 'function',
        function: searchByUrlSchema
    },
    {
        type: 'function',
        function: searchOnlineSchema
    },
    {
        type: 'function',
        function: summarizeUrlSchema
    },
    {
        type: 'function',
        function: summarizeFileSchema
    },
    {
        type: 'function',
        function: clockSchema
    },
    {
        type: 'function',
        function: reverseGeocodeSchema
    },
    {
        type: 'function',
        function: showPoisOnMapSchema
    },
    {
        type: 'function',
        function: runPythonCodeSchema
    },
    {
        type: 'function',
        function: createImagesSchema
    },
    {
        type: 'function',
        function: generateArtifactsSchema
    },
    {
        type: 'function',
        function: generateSmilesFromMoleculeSchema
    },
];

export const anthropicTools: AnthropicTool[] = openAITools.map((item) => {
    return {
        name: item.function.name,
        description: item.function.description,
        input_schema: item.function.parameters
    } as AnthropicTool
})

export const requiredToolNames = [
    TOOL_NAME.SEARCH_BY_FILES,
    TOOL_NAME.SUMMARIZE_URL,
    TOOL_NAME.SUMMARIZE_FILE,
    TOOL_NAME.CLOCK,
    TOOL_NAME.REVERSE_GEOCODE,
    TOOL_NAME.SHOW_POIS_ON_MAP,
    TOOL_NAME.GENERATE_ARTIFACTS,
];

export function generateOpenAITools(tools: ToolControlParam) {

    const intersection = intersectionBy(toolsControl, tools, 'id');
    let filteredToolNames = [...intersection.flatMap(item => item.tools), ...requiredToolNames];
    return openAITools.filter(tool => filteredToolNames.includes(tool.function.name)).map((tool) => {
        return {
            ...tool,
            function: {
                ...tool.function,
                description: tool.function.name === TOOL_NAME.RUN_PYTHON_CODE ? toolInfoMap.get(TOOL_NAME.RUN_PYTHON_CODE)?.description?.replaceAll(/\n[^\S\r\n]+/g, '\n').substring(0, 1024) : tool.function.description,
            }
        } as ChatCompletionTool
    });

}

export function generateAnthropicTools(openAITools: ChatCompletionTool[]) {
    return openAITools.map((item) => {
        if (item.function.name === TOOL_NAME.RUN_PYTHON_CODE) {
            return {
                name: item.function.name,
                description: toolInfoMap.get(TOOL_NAME.RUN_PYTHON_CODE)?.description,
                input_schema: item.function.parameters
            } as AnthropicTool
        }
        return {
            name: item.function.name,
            description: item.function.description,
            input_schema: item.function.parameters
        } as AnthropicTool
    })
}

